@model List<Technoloway.Core.Entities.DataUploadLog>
@{
    ViewData["Title"] = "Upload History";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="page-title">
                            <i class="fas fa-history me-2"></i>Upload History
                        </h1>
                        <p class="page-subtitle">View and manage data upload logs</p>
                    </div>
                    <div>
                        <a href="@Url.Action("Index")" class="btn btn-primary">
                            <i class="fas fa-upload me-1"></i>New Upload
                        </a>
                        <button type="button" class="btn btn-outline-warning ms-2" onclick="cleanupOldLogs()">
                            <i class="fas fa-broom me-1"></i>Cleanup Old Logs
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>Upload Logs
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>File Name</th>
                                        <th>Entity</th>
                                        <th>Operation</th>
                                        <th>Status</th>
                                        <th>Records</th>
                                        <th>Uploaded By</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var log in Model)
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="@GetFileIcon(log.FileType) me-2"></i>
                                                    <div>
                                                        <div class="fw-medium">@log.FileName</div>
                                                        <small class="text-muted">@log.FileType • @FormatFileSize(log.FileSizeBytes)</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@log.TargetEntity</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">@log.Operation</span>
                                            </td>
                                            <td>
                                                <span class="badge @GetStatusBadgeClass(log.Status)">
                                                    <i class="@GetStatusIcon(log.Status) me-1"></i>
                                                    @log.Status
                                                </span>
                                            </td>
                                            <td>
                                                <div class="text-center">
                                                    <div class="fw-medium">@log.RecordsProcessed</div>
                                                    @if (log.RecordsProcessed > 0)
                                                    {
                                                        <div class="small">
                                                            <span class="text-success">✓ @log.RecordsSuccessful</span>
                                                            @if (log.RecordsFailed > 0)
                                                            {
                                                                <span class="text-danger ms-1">✗ @log.RecordsFailed</span>
                                                            }
                                                        </div>
                                                    }
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <div class="fw-medium">@log.UploadedByUserName</div>
                                                    <small class="text-muted">@log.UploadedByUserId</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <div class="fw-medium">@log.StartedAt.ToString("MMM dd, yyyy")</div>
                                                    <small class="text-muted">@log.StartedAt.ToString("HH:mm:ss")</small>
                                                    @if (log.CompletedAt.HasValue)
                                                    {
                                                        <div class="small text-muted">
                                                            Duration: @((log.CompletedAt.Value - log.StartedAt).TotalSeconds.ToString("F1"))s
                                                        </div>
                                                    }
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="@Url.Action("Details", new { id = log.Id })" 
                                                       class="btn btn-outline-primary" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    @if (log.CanRollback && !log.RolledBackAt.HasValue && log.Status == "Completed")
                                                    {
                                                        <button type="button" class="btn btn-outline-warning" 
                                                                onclick="rollbackUpload(@log.Id)" title="Rollback">
                                                            <i class="fas fa-undo"></i>
                                                        </button>
                                                    }
                                                    @if (log.RolledBackAt.HasValue)
                                                    {
                                                        <span class="btn btn-outline-secondary disabled" title="Already Rolled Back">
                                                            <i class="fas fa-undo"></i>
                                                        </span>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if (ViewBag.CurrentPage != null && ViewBag.PageSize != null)
                        {
                            var currentPage = (int)ViewBag.CurrentPage;
                            var pageSize = (int)ViewBag.PageSize;
                            var totalPages = (int)Math.Ceiling((double)Model.Count / pageSize);

                            if (totalPages > 1)
                            {
                                <nav aria-label="Upload history pagination">
                                    <ul class="pagination justify-content-center">
                                        @if (currentPage > 1)
                                        {
                                            <li class="page-item">
                                                <a class="page-link" href="@Url.Action("History", new { page = currentPage - 1, pageSize })">
                                                    <i class="fas fa-chevron-left"></i>
                                                </a>
                                            </li>
                                        }

                                        @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                                        {
                                            <li class="page-item @(i == currentPage ? "active" : "")">
                                                <a class="page-link" href="@Url.Action("History", new { page = i, pageSize })">@i</a>
                                            </li>
                                        }

                                        @if (currentPage < totalPages)
                                        {
                                            <li class="page-item">
                                                <a class="page-link" href="@Url.Action("History", new { page = currentPage + 1, pageSize })">
                                                    <i class="fas fa-chevron-right"></i>
                                                </a>
                                            </li>
                                        }
                                    </ul>
                                </nav>
                            }
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No upload history found</h5>
                            <p class="text-muted">Start by uploading your first data file.</p>
                            <a href="@Url.Action("Index")" class="btn btn-primary">
                                <i class="fas fa-upload me-1"></i>Upload Data
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="confirmMessage">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="confirmAction">Confirm</button>
            </div>
        </div>
    </div>
</div>

@functions {
    private string GetFileIcon(string fileType)
    {
        return fileType switch
        {
            "CSV" => "fas fa-file-csv text-success",
            "Excel" => "fas fa-file-excel text-success",
            "JSON" => "fas fa-file-code text-info",
            "XML" => "fas fa-file-code text-warning",
            "SQL" => "fas fa-database text-primary",
            "TSV" => "fas fa-file-alt text-secondary",
            _ => "fas fa-file text-muted"
        };
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "Completed" => "bg-success",
            "Processing" => "bg-primary",
            "Failed" => "bg-danger",
            "Rolled Back" => "bg-warning",
            _ => "bg-secondary"
        };
    }

    private string GetStatusIcon(string status)
    {
        return status switch
        {
            "Completed" => "fas fa-check-circle",
            "Processing" => "fas fa-spinner fa-spin",
            "Failed" => "fas fa-exclamation-triangle",
            "Rolled Back" => "fas fa-undo",
            _ => "fas fa-clock"
        };
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }
}

@section Scripts {
    <script>
        function rollbackUpload(logId) {
            $('#confirmMessage').html(`
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> This will restore the database to its state before this upload.
                    All changes made by this upload will be lost.
                </div>
                <p>Are you sure you want to rollback this upload?</p>
            `);
            
            $('#confirmAction').off('click').on('click', function() {
                performRollback(logId);
            });
            
            $('#confirmModal').modal('show');
        }

        function performRollback(logId) {
            $.post('@Url.Action("Rollback")', {
                id: logId,
                __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
            })
            .done(function(response) {
                $('#confirmModal').modal('hide');
                if (response.success) {
                    showAlert('success', response.message);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showAlert('danger', response.message);
                }
            })
            .fail(function() {
                $('#confirmModal').modal('hide');
                showAlert('danger', 'Rollback request failed. Please try again.');
            });
        }

        function cleanupOldLogs() {
            $('#confirmMessage').html(`
                <p>This will permanently delete upload logs older than 30 days and their associated backup files.</p>
                <p>Are you sure you want to continue?</p>
            `);
            
            $('#confirmAction').off('click').on('click', function() {
                performCleanup();
            });
            
            $('#confirmModal').modal('show');
        }

        function performCleanup() {
            $.post('@Url.Action("CleanupOldLogs")', {
                daysToKeep: 30,
                __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
            })
            .done(function(response) {
                $('#confirmModal').modal('hide');
                if (response.success) {
                    showAlert('success', response.message);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showAlert('danger', response.message);
                }
            })
            .fail(function() {
                $('#confirmModal').modal('hide');
                showAlert('danger', 'Cleanup request failed. Please try again.');
            });
        }

        function showAlert(type, message) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('.container-fluid').prepend(alertHtml);
        }
    </script>
}
